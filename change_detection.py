import rasterio
import numpy as np
import cv2
import base64
import matplotlib.pyplot as plt
import os
import matplotlib.cm
import matplotlib.colors
import json
from typing import List, Tuple, Optional, Dict, Any

class ChangeDetectionModel:
    # Configuration for index-specific color schemes and min/max valuess
    INDEX_CONFIG = {
    "LAI": {"min": 0, "max": 10, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "SMI": {"min": 0, "max": 1, "negativeColor": "red", "positiveColor": "blue", "stableColor": "white", "colormap": "BrBG"},  # Soil Moisture
    "MNDWI": {"min": -1, "max": 1, "negativeColor": "gray", "positiveColor": "blue", "stableColor": "white", "colormap": "Blues"},  # Water
    "NDMI": {"min": -1, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "BrBG"},  # Moisture
    "FAPAR": {"min": 0, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "CCC": {"min": 0, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "CWC": {"min": 0, "max": 1, "negativeColor": "lightgray", "positiveColor": "blue", "stableColor": "white", "colormap": "coolwarm_r"},  # Canopy Water Content
    "IRECI": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    "MSAVI2": {"min": -1, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "NMDI": {"min": -1, "max": 1, "negativeColor": "brown", "positiveColor": "blue", "stableColor": "white", "colormap": "BrBG"},
    "WIW": {"min": 0, "max": 1, "negativeColor": "green", "positiveColor": "blue", "stableColor": "white", "colormap": "coolwarm_r"},
    "SOC_VIS": {"min": 0, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "copper"},
    "SOC_SWIR": {"min": 0, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "copper"},
    "NDVI": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    "NDWI": {"min": -1, "max": 1, "negativeColor": "gray", "positiveColor": "blue", "stableColor": "white", "colormap": "Blues"},
    "EVI": {"min": -1, "max": 6, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    "NDRE": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    "ARI1": {"min": 0, "max": 2, "negativeColor": "darkred", "positiveColor": "orange", "stableColor": "white", "colormap": "magma"},  # Anthocyanin
    "CAR_RE": {"min": 0, "max": 1, "negativeColor": "red", "positiveColor": "yellowgreen", "stableColor": "white", "colormap": "YlGnBu"},  # Carotenoid
    "CL_RE": {"min": 0, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},  # Chlorophyll
    "NDREX": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    "NMDI_SOIL": {"min": -1, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "BrBG"},
    "NMDI_VEG": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "SI": {"min": 0, "max": 1, "negativeColor": "brown", "positiveColor": "green", "stableColor": "white", "colormap": "YlGn"},
    "YRSI": {"min": 0, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"},
    # Default fallback
    "DEFAULT": {"min": -1, "max": 1, "negativeColor": "red", "positiveColor": "green", "stableColor": "white", "colormap": "RdYlGn"}
}

    COLOR_MAP = {
    "red": [0, 0, 255],      # BGR
    "green": [0, 255, 0],    # BGR
    "blue": [255, 0, 0],     # BGR
    "brown": [40, 70, 130],  # BGR example for a darker brown
    "white": [255, 255, 255],# BGR
    "gray": [150, 150, 150], # BGR
    "yellow": [0, 255, 255], # BGR for yellow
    "lightgray": [200, 200, 200] # BGR for light gray
    }

    # Current index type being processed
    _current_index = "DEFAULT"

    def set_thresholds_for_index(self, index_type: str) -> None:
        """
        Set the current index type for processing.

        Args:
            index_type (str): The type of index being processed
        """
        self._current_index = index_type.upper()
    def create_polygon_mask(self, image_shape: tuple, polygon_coords: List[Tuple[float, float]]) -> np.ndarray:
        """
        Create a binary mask using polygon coordinates.

        Args:
            image_shape (tuple): Shape of the image (height, width)
            polygon_coords (List[Tuple[float, float]]): List of (x, y) coordinates

        Returns:
            np.ndarray: Binary mask where 1 indicates inside polygon, 0 outside
        """
        mask = np.zeros(image_shape, dtype=np.uint8)
        polygon_np = np.array([polygon_coords], dtype=np.int32)
        cv2.fillPoly(mask, polygon_np, 1)
        return mask

    def apply_mask(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        Apply mask to image, setting pixels outside mask to 0.

        Args:
            image (np.ndarray): Input image
            mask (np.ndarray): Binary mask

        Returns:
            np.ndarray: Masked image with 0 values outside mask
        """
        return np.where(mask == 1, image, 0.0)



    def calculate_difference(self, file1: str, file2: str, polygon_coords: Optional[List[Tuple[float, float]]] = None) -> Tuple[np.ndarray, dict]:
        """
        Calculate difference between two images with optional polygon masking.

        Args:
            file1 (str): Path to first image
            file2 (str): Path to second image
            polygon_coords (Optional[List[Tuple[float, float]]]): Optional polygon coordinates

        Returns:
            Tuple[np.ndarray, dict]: Difference array and metadata dictionary
        """
        metadata = {"original_shape": None, "polygon_mask": None, "polygon_coords": None, "bbox": None}

        with rasterio.open(file1) as src1, rasterio.open(file2) as src2:
            img1 = src1.read(1)
            img2 = src2.read(1)

            metadata["original_shape"] = img1.shape

            # Apply polygon mask if provided
            if polygon_coords:
                try:
                    # Convert geographic coordinates to pixel coordinates if needed
                    if abs(polygon_coords[0][0]) <= 180:  # Assuming geographic coordinates
                        transform = src1.transform
                        pixel_coords = [~transform * (x, y) for x, y in polygon_coords]
                        polygon_coords = [(int(col), int(row)) for col, row in pixel_coords]

                    # Store the polygon coordinates in metadata
                    metadata["polygon_coords"] = polygon_coords

                    # Create polygon mask
                    mask = self.create_polygon_mask(img1.shape, polygon_coords)
                    metadata["polygon_mask"] = mask

                    # Apply mask to both images
                    img1 = self.apply_mask(img1, mask)
                    img2 = self.apply_mask(img2, mask)
                except Exception as e:
                    raise ValueError(f"Error applying polygon mask: {str(e)}")

            return img2 - img1, metadata

    def classify_changes(self, diff: np.ndarray, metadata: dict = None, threshold: float = 0.0) -> dict:
        """
        Classify changes in the difference array.
        Any positive value is considered positive change, any negative value is considered negative change.
        Zeros are considered stable or outside the polygon.
        If threshold > 0, only values above the threshold are considered as changes.

        Args:
            diff (np.ndarray): Difference array
            metadata (dict, optional): Metadata from calculate_difference including polygon mask
            threshold (float, optional): Threshold value (0.0-1.0) to filter out pixels with values below the threshold.
                                        0.0 means no filtering, 1.0 means only show the highest values.

        Returns:
            dict: Classification results with percentages
        """
        if metadata and metadata.get("polygon_mask") is not None:
            # If we have a polygon mask, use it to count only pixels inside the polygon
            mask = metadata["polygon_mask"]
            inside_polygon = (mask == 1)

            # Apply threshold if needed
            if threshold > 0.0:
                # Calculate absolute values for thresholding
                abs_diff = np.abs(diff)
                if np.any(abs_diff > 0):
                    # Normalize absolute values to 0-1 range
                    normalized_abs_diff = abs_diff / np.max(abs_diff)
                    # Create threshold mask
                    threshold_mask = normalized_abs_diff >= threshold
                    # Apply threshold to masks
                    pos_mask = (diff > 0) & inside_polygon & threshold_mask
                    neg_mask = (diff < 0) & inside_polygon & threshold_mask
                    stable_mask = ((diff == 0) | (~threshold_mask)) & inside_polygon
                else:
                    # If all values are 0, everything is stable
                    pos_mask = np.zeros_like(diff, dtype=bool)
                    neg_mask = np.zeros_like(diff, dtype=bool)
                    stable_mask = inside_polygon
            else:
                # No threshold filtering
                pos_mask = (diff > 0) & inside_polygon
                neg_mask = (diff < 0) & inside_polygon
                stable_mask = (diff == 0) & inside_polygon

            # Count pixels
            total = np.sum(inside_polygon)
            pos = np.sum(pos_mask)
            neg = np.sum(neg_mask)
            stable = np.sum(stable_mask)
        else:
            # If no mask, count all pixels
            # Apply threshold if needed
            if threshold > 0.0:
                # Calculate absolute values for thresholding
                abs_diff = np.abs(diff)
                if np.any(abs_diff > 0):
                    # Normalize absolute values to 0-1 range
                    normalized_abs_diff = abs_diff / np.max(abs_diff)
                    # Create threshold mask
                    threshold_mask = normalized_abs_diff >= threshold
                    # Apply threshold to masks
                    pos_mask = (diff > 0) & threshold_mask
                    neg_mask = (diff < 0) & threshold_mask
                    stable_mask = (diff == 0) | (~threshold_mask)
                else:
                    # If all values are 0, everything is stable
                    pos_mask = np.zeros_like(diff, dtype=bool)
                    neg_mask = np.zeros_like(diff, dtype=bool)
                    stable_mask = np.ones_like(diff, dtype=bool)
            else:
                # No threshold filtering
                pos_mask = (diff > 0)
                neg_mask = (diff < 0)
                stable_mask = (diff == 0)

            # Count pixels
            total = diff.size
            pos = np.sum(pos_mask)
            neg = np.sum(neg_mask)
            stable = np.sum(stable_mask)

        if total == 0:
            return {
                "positive_percentage": 0,
                "negative_percentage": 0,
                "stable_percentage": 0
            }

        return {
            "positive_percentage": (pos / total) * 100,
            "negative_percentage": (neg / total) * 100,
            "stable_percentage": (stable / total) * 100
        }

    def generate_colored_difference(self, diff: np.ndarray, metadata: Optional[dict] = None, threshold_norm: float = 0.0) -> Optional[str]:
        """
        Generate graduated colored visualization of difference array using a continuous colormap.
        Areas below a relative threshold are shown in a 'stable' color.
        NaNs and areas outside a polygon (if provided) are transparent.

        Args:
            diff (np.ndarray): Difference array (can contain NaNs).
            metadata (Optional[dict]): Metadata, potentially including "polygon_mask_applied_to_diff" (boolean)
                                       and "bbox_yx" (ymin, ymax, xmin, xmax).
            threshold_norm (float): Relative threshold (0.0-1.0). Values with absolute magnitude
                                    below threshold * max_abs_diff_in_valid_area are "stable".
                                    0.0 means only exact zeros (or NaNs) are not colored by colormap.

        Returns:
            Optional[str]: Base64 encoded BGRA TIFF image or None if generation fails.
        """
        if diff is None:
            print("Error: Input difference array is None.")
            return None

        index_config = self.INDEX_CONFIG.get(self._current_index, self.INDEX_CONFIG["DEFAULT"])
        stable_color_bgr_name = index_config["stableColor"]
        stable_color_bgr = np.array(self.COLOR_MAP.get(stable_color_bgr_name, self.COLOR_MAP["white"]), dtype=np.uint8)
        colormap_name = index_config.get("colormap", "RdYlGn")

        original_height, original_width = diff.shape
        
        # Initialize full size BGRA image (transparent black)
        # OpenCV expects BGR(A) order for color channels
        full_size_bgra = np.zeros((original_height, original_width, 4), dtype=np.uint8)

        # Determine the actual processing mask: where data is not NaN
        # And, if polygon_mask exists in metadata, only within the polygon
        valid_data_mask = ~np.isnan(diff)
        if metadata and metadata.get("polygon_mask_applied_to_diff") is not None:
            polygon_mask_bool = metadata["polygon_mask_applied_to_diff"]
            if polygon_mask_bool.shape == diff.shape:
                valid_data_mask &= polygon_mask_bool
            else:
                print("Warning: polygon_mask_applied_to_diff shape mismatch. Ignoring polygon for visualization.")
        
        # Pixels to be considered for colormapping or stable color
        # pixels_to_color_mask = valid_data_mask.copy() # Not strictly needed with current logic
        
        # --- Apply continuous colormap to significant changes ---
        # Select difference values within the valid_data_mask for normalization
        diff_values_for_norm = diff[valid_data_mask]

        if diff_values_for_norm.size > 0:
            # Determine normalization range for the colormap (percentile-based)
            norm_min = np.percentile(diff_values_for_norm, 2)
            norm_max = np.percentile(diff_values_for_norm, 98)

            if norm_min >= norm_max: # Handle flat data or single value
                norm_min = np.min(diff_values_for_norm) # Use actual min/max if percentiles are problematic
                norm_max = np.max(diff_values_for_norm)
            if norm_min == norm_max: # Still same (e.g. all zeros after masking)
                 norm_min -= 0.01 # Create a tiny range to avoid division by zero or flat colormap
                 norm_max += 0.01
            
            # Normalize and apply colormap
            norm = matplotlib.colors.Normalize(vmin=norm_min, vmax=norm_max, clip=True)
            cmap = matplotlib.cm.get_cmap(colormap_name)
            
            # Apply colormap to all non-NaN values in the original diff array.
            # cmap needs non-NaN values, handle NaNs by temp replacing or masking input to cmap
            # Create a temporary copy of diff to apply colormap, replace NaNs with a value
            # within the norm range to avoid issues with cmap, then use valid_data_mask to apply.
            temp_diff_for_cmap = np.copy(diff)
            # Replace NaNs (or values outside valid_data_mask) with a value that won't error out cmap
            # and won't affect valid data (e.g., norm_min or an average).
            # The important part is that we only use the colored results where valid_data_mask is True.
            temp_diff_for_cmap[~valid_data_mask] = norm_min 
            
            rgba_float_full = cmap(norm(temp_diff_for_cmap)) # (H,W,4) float [0,1] for the whole image
            rgb_uint8_full = (rgba_float_full[:, :, :3] * 255).astype(np.uint8)
            bgr_uint8_full = rgb_uint8_full[:, :, ::-1] # Convert RGB to BGR for OpenCV

            # Assign graduated colors to BGR channels where valid_data_mask is true
            full_size_bgra[valid_data_mask, :3] = bgr_uint8_full[valid_data_mask]
            full_size_bgra[valid_data_mask, 3] = 255 # Make these areas opaque

            # --- Apply stable color for values below threshold ---
            # Threshold is relative to max absolute difference within the valid_data_mask
            abs_diff_in_valid_area = np.abs(diff[valid_data_mask]) # Use only valid diffs for max_abs_val
            max_abs_val = np.max(abs_diff_in_valid_area) if abs_diff_in_valid_area.size > 0 else 0

            if max_abs_val > 0 and threshold_norm > 0.0:
                threshold_value_actual = threshold_norm * max_abs_val
                # Stable if abs diff < threshold_value_actual, AND it's a valid data pixel
                is_stable_mask = (np.abs(diff) < threshold_value_actual) & valid_data_mask
                full_size_bgra[is_stable_mask, :3] = stable_color_bgr
                full_size_bgra[is_stable_mask, 3] = 255 # Ensure stable areas are opaque
            elif threshold_norm == 0.0: # Only exact zeros are stable (if not NaN)
                is_stable_mask = (diff == 0) & valid_data_mask # Must be valid to be stable
                full_size_bgra[is_stable_mask, :3] = stable_color_bgr
                full_size_bgra[is_stable_mask, 3] = 255
            # If max_abs_val is 0 (all diffs in valid area are 0), all valid_data_mask pixels are effectively stable
            elif max_abs_val == 0.0 and np.any(valid_data_mask): # Check if there are any valid pixels
                 is_stable_mask = valid_data_mask # All valid pixels are stable
                 full_size_bgra[is_stable_mask, :3] = stable_color_bgr
                 full_size_bgra[is_stable_mask, 3] = 255

        else: # No valid data pixels to color at all (e.g., all NaN or empty polygon)
            print("Warning: No valid data pixels found in the difference array to generate colored output.")
            # full_size_bgra remains transparent black as initialized

        # Ensure areas that were never part of valid_data_mask (e.g. original NaNs, outside polygon)
        # have their alpha channel set to 0 (transparent).
        # This is crucial if they were not handled by the logic above (e.g. if diff_values_for_norm.size was 0).
        full_size_bgra[~valid_data_mask, 3] = 0

        # --- Crop to polygon bounding box if polygon mask is present ---
        if metadata and metadata.get("polygon_mask") is not None:
            polygon_mask = metadata["polygon_mask"]
            # Find bounding box of the polygon
            rows = np.any(polygon_mask, axis=1)
            cols = np.any(polygon_mask, axis=0)
            if np.any(rows) and np.any(cols):
                ymin, ymax = np.where(rows)[0][[0, -1]]
                xmin, xmax = np.where(cols)[0][[0, -1]]
                # Crop the image and mask
                cropped_bgra = full_size_bgra[ymin:ymax+1, xmin:xmax+1, :]
                cropped_mask = polygon_mask[ymin:ymax+1, xmin:xmax+1]
                # Set alpha=0 for pixels outside the polygon (even inside the crop)
                cropped_bgra[cropped_mask == 0, 3] = 0
                output_bgra = cropped_bgra
            else:
                # Polygon mask is empty, return fully transparent image
                output_bgra = np.zeros_like(full_size_bgra)
        else:
            output_bgra = full_size_bgra

        # Encode the output BGRA image to TIFF
        ok, buf = cv2.imencode('.tiff', output_bgra)
        if ok:
            return base64.b64encode(buf).decode('utf-8')
        else:
            print("Error: Failed to encode TIFF image.")
            return None




    def smooth_cluster_shape(self, mask: np.ndarray, kernel_size: int = 5, iterations: int = 2) -> np.ndarray:
        """
        Smooth cluster shapes using morphological operations to make them look less pixelated.

        Args:
            mask (np.ndarray): Binary mask where True indicates the cluster region
            kernel_size (int): Size of the morphological kernel (default: 5)
            iterations (int): Number of iterations for morphological operations (default: 2)

        Returns:
            np.ndarray: Smoothed binary mask
        """
        # Convert boolean mask to uint8
        mask_uint8 = mask.astype(np.uint8) * 255

        # Create morphological kernel (elliptical for smoother results)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))

        # Apply morphological closing to fill small gaps and smooth edges
        smoothed = cv2.morphologyEx(mask_uint8, cv2.MORPH_CLOSE, kernel, iterations=iterations)

        # Apply morphological opening to remove small noise and smooth further
        smoothed = cv2.morphologyEx(smoothed, cv2.MORPH_OPEN, kernel, iterations=1)

        # Apply Gaussian blur for additional smoothing
        smoothed = cv2.GaussianBlur(smoothed, (3, 3), 0)

        # Threshold back to binary
        _, smoothed = cv2.threshold(smoothed, 127, 255, cv2.THRESH_BINARY)

        return smoothed.astype(bool)

    def draw_contour_lines(self, mask: np.ndarray, colored_image: np.ndarray, contour_color: list = [0, 0, 0], thickness: int = 1) -> np.ndarray:
        """
        Draw contour lines around a binary mask.

        Args:
            mask (np.ndarray): Binary mask where True indicates the region to contour
            colored_image (np.ndarray): The colored image to draw contours on
            contour_color (list): BGR color for contour lines (default: black)
            thickness (int): Thickness of contour lines (default: 1)

        Returns:
            np.ndarray: Image with contour lines drawn
        """
        # Convert boolean mask to uint8
        mask_uint8 = mask.astype(np.uint8) * 255

        # Find contours
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Draw contours on the colored image
        result = colored_image.copy()
        cv2.drawContours(result, contours, -1, contour_color, thickness)

        return result

    def generate_cluster_image(self, diff: np.ndarray, metadata: dict = None, n_clusters: int = 3, threshold: float = 0.0) -> dict:
        """
        Generate cluster visualizations showing the most significant zones in the difference array.
        Creates separate images for negative, positive, and stable clusters with smooth, shape-like appearance.
        Applies morphological operations to make clusters look less pixelated and more like coherent shapes.
        Applies a threshold to filter out pixels with values below the threshold.

        Args:
            diff (np.ndarray): Difference array
            metadata (dict, optional): Metadata from calculate_difference including polygon mask
            n_clusters (int): Number of clusters to identify (default: 3)
            threshold (float, optional): Threshold value (0.0-1.0) to filter out pixels with values below the threshold.
                                        0.0 means no filtering, 1.0 means only show the highest values.

        Returns:
            dict: Dictionary containing base64 encoded TIFF images for negative, positive, and stable clusters with smooth shapes
        """
        try:
            from sklearn.cluster import KMeans

            result = {
                "negative": None,
                "positive": None,
                "stable": None
            }

            index_config = self.INDEX_CONFIG.get(self._current_index, self.INDEX_CONFIG["DEFAULT"])
            colormap_name = index_config.get("colormap", "RdYlGn")
            background_color = self.COLOR_MAP["lightgray"]

            if metadata and metadata.get("polygon_mask") is not None:
                mask = metadata["polygon_mask"]
                rows = np.any(mask, axis=1)
                cols = np.any(mask, axis=0)
                if not np.any(rows) or not np.any(cols):
                    return result
                ymin, ymax = np.where(rows)[0][[0, -1]]
                xmin, xmax = np.where(cols)[0][[0, -1]]
                diff_cropped = diff[ymin:ymax+1, xmin:xmax+1]
                mask_cropped = mask[ymin:ymax+1, xmin:xmax+1]
                inside_polygon = (mask_cropped == 1)
                height, width = diff_cropped.shape

                # Prepare colormap normalization for the cropped diff
                diff_for_norm = diff_cropped[inside_polygon]
                if diff_for_norm.size > 0:
                    norm_min = np.percentile(diff_for_norm, 2)
                    norm_max = np.percentile(diff_for_norm, 98)
                    if norm_min >= norm_max:
                        norm_min = np.min(diff_for_norm)
                        norm_max = np.max(diff_for_norm)
                    if norm_min == norm_max:
                        norm_min -= 0.01
                        norm_max += 0.01
                    norm = matplotlib.colors.Normalize(vmin=norm_min, vmax=norm_max, clip=True)
                    cmap = matplotlib.cm.get_cmap(colormap_name)
                else:
                    norm = None
                    cmap = None

                # Helper to apply colormap to a mask
                def colormap_rgba(diff_crop, mask):
                    rgba = np.zeros((height, width, 4), dtype=np.uint8)
                    if cmap is not None and np.any(mask):
                        normed = norm(diff_crop)
                        rgba_float = cmap(normed)
                        rgb_uint8 = (rgba_float[:, :, :3] * 255).astype(np.uint8)
                        bgr_uint8 = rgb_uint8[:, :, ::-1]
                        rgba[..., :3] = bgr_uint8
                        rgba[..., 3] = 0
                        rgba[mask, :3] = bgr_uint8[mask]
                        rgba[mask, 3] = 255
                    return rgba

                # --- Cluster masks ---
                # ...existing code for thresholding and mask creation...
                if threshold > 0.0:
                    abs_diff = np.abs(diff_cropped)
                    if np.any(abs_diff > 0):
                        normalized_abs_diff = abs_diff / np.max(abs_diff)
                        threshold_mask = normalized_abs_diff >= threshold
                        neg_mask = (diff_cropped < 0) & inside_polygon & threshold_mask
                        pos_mask = (diff_cropped > 0) & inside_polygon & threshold_mask
                        stable_mask = ((diff_cropped == 0) | (~threshold_mask)) & inside_polygon
                    else:
                        neg_mask = np.zeros_like(diff_cropped, dtype=bool)
                        pos_mask = np.zeros_like(diff_cropped, dtype=bool)
                        stable_mask = inside_polygon
                else:
                    neg_mask = (diff_cropped < 0) & inside_polygon
                    pos_mask = (diff_cropped > 0) & inside_polygon
                    stable_mask = (diff_cropped == 0) & inside_polygon

                # Negative cluster
                if np.sum(neg_mask) > 0:
                    y_coords, x_coords = np.where(neg_mask)
                    neg_values = diff_cropped[neg_mask]
                    X = np.column_stack([x_coords, y_coords, neg_values * 10])
                    kmeans = KMeans(n_clusters=min(n_clusters, len(X)), random_state=42)
                    clusters = kmeans.fit_predict(X)
                    cluster_means = []
                    for i in range(kmeans.n_clusters):
                        cluster_mask = (clusters == i)
                        if np.any(cluster_mask):
                            cluster_mean = np.mean(neg_values[cluster_mask])
                            cluster_means.append((i, cluster_mean))
                    cluster_means.sort(key=lambda x: x[1])
                    mask_to_show = np.zeros((height, width), dtype=bool)
                    if cluster_means:
                        most_negative_cluster = cluster_means[0][0]
                        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
                            if clusters[i] == most_negative_cluster:
                                # Only include pixels that are truly negative in diff_cropped
                                if diff_cropped[y, x] < 0:
                                    mask_to_show[y, x] = True
                        mask_to_show = self.smooth_cluster_shape(mask_to_show, kernel_size=5, iterations=2)
                    # Only apply colormap to negative values in the mask
                    colormap_mask = mask_to_show & (diff_cropped < 0)
                    rgba = colormap_rgba(diff_cropped, colormap_mask)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["negative"] = base64.b64encode(buf).decode('utf-8')

                # Positive cluster
                if np.sum(pos_mask) > 0:
                    y_coords, x_coords = np.where(pos_mask)
                    pos_values = diff_cropped[pos_mask]
                    X = np.column_stack([x_coords, y_coords, pos_values * 10])
                    kmeans = KMeans(n_clusters=min(n_clusters, len(X)), random_state=42)
                    clusters = kmeans.fit_predict(X)
                    cluster_means = []
                    for i in range(kmeans.n_clusters):
                        cluster_mask = (clusters == i)
                        if np.any(cluster_mask):
                            cluster_mean = np.mean(pos_values[cluster_mask])
                            cluster_means.append((i, cluster_mean))
                    cluster_means.sort(key=lambda x: x[1], reverse=True)
                    mask_to_show = np.zeros((height, width), dtype=bool)
                    if cluster_means:
                        most_positive_cluster = cluster_means[0][0]
                        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
                            if clusters[i] == most_positive_cluster:
                                # Only include pixels that are truly positive in diff_cropped
                                if diff_cropped[y, x] > 0:
                                    mask_to_show[y, x] = True
                        mask_to_show = self.smooth_cluster_shape(mask_to_show, kernel_size=5, iterations=2)
                    # Only apply colormap to positive values in the mask
                    colormap_mask = mask_to_show & (diff_cropped > 0)
                    rgba = colormap_rgba(diff_cropped, colormap_mask)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["positive"] = base64.b64encode(buf).decode('utf-8')

                # Stable cluster
                if np.sum(stable_mask) > 0:
                    mask_to_show = stable_mask & (diff_cropped == 0)
                    rgba = colormap_rgba(diff_cropped, mask_to_show)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["stable"] = base64.b64encode(buf).decode('utf-8')

            else:
                # If no mask, create visualization for the entire image
                height, width = diff.shape

                # Create masks for different types of pixels
                # Apply threshold if needed
                if threshold > 0.0:
                    # Calculate absolute values for thresholding
                    abs_diff = np.abs(diff)
                    if np.any(abs_diff > 0):
                        # Normalize absolute values to 0-1 range
                        normalized_abs_diff = abs_diff / np.max(abs_diff)
                        # Create threshold mask
                        threshold_mask = normalized_abs_diff >= threshold
                        # Apply threshold to masks
                        neg_mask = (diff < 0) & threshold_mask
                        pos_mask = (diff > 0) & threshold_mask
                        stable_mask = (diff == 0) | (~threshold_mask)
                    else:
                        # If all values are 0, everything is stable
                        neg_mask = np.zeros_like(diff, dtype=bool)
                        pos_mask = np.zeros_like(diff, dtype=bool)
                        stable_mask = np.ones_like(diff, dtype=bool)
                else:
                    # No threshold filtering
                    neg_mask = (diff < 0)
                    pos_mask = (diff > 0)
                    stable_mask = (diff == 0)

                # Negative cluster
                if np.sum(neg_mask) > 0:
                    y_coords, x_coords = np.where(neg_mask)
                    neg_values = diff[neg_mask]
                    X = np.column_stack([x_coords, y_coords, neg_values * 10])
                    kmeans = KMeans(n_clusters=min(n_clusters, len(X)), random_state=42)
                    clusters = kmeans.fit_predict(X)
                    cluster_means = []
                    for i in range(kmeans.n_clusters):
                        cluster_mask = (clusters == i)
                        if np.any(cluster_mask):
                            cluster_mean = np.mean(neg_values[cluster_mask])
                            cluster_means.append((i, cluster_mean))
                    cluster_means.sort(key=lambda x: x[1])
                    mask_to_show = np.zeros((height, width), dtype=bool)
                    if cluster_means:
                        most_negative_cluster = cluster_means[0][0]
                        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
                            if clusters[i] == most_negative_cluster:
                                mask_to_show[y, x] = True
                        mask_to_show = self.smooth_cluster_shape(mask_to_show, kernel_size=5, iterations=2)
                    rgba = colormap_rgba(diff, mask_to_show)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["negative"] = base64.b64encode(buf).decode('utf-8')

                # Positive cluster
                if np.sum(pos_mask) > 0:
                    y_coords, x_coords = np.where(pos_mask)
                    pos_values = diff[pos_mask]
                    X = np.column_stack([x_coords, y_coords, pos_values * 10])
                    kmeans = KMeans(n_clusters=min(n_clusters, len(X)), random_state=42)
                    clusters = kmeans.fit_predict(X)
                    cluster_means = []
                    for i in range(kmeans.n_clusters):
                        cluster_mask = (clusters == i)
                        if np.any(cluster_mask):
                            cluster_mean = np.mean(pos_values[cluster_mask])
                            cluster_means.append((i, cluster_mean))
                    cluster_means.sort(key=lambda x: x[1], reverse=True)
                    mask_to_show = np.zeros((height, width), dtype=bool)
                    if cluster_means:
                        most_positive_cluster = cluster_means[0][0]
                        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
                            if clusters[i] == most_positive_cluster:
                                # Only include pixels that are truly positive in diff_cropped
                                if diff_cropped[y, x] > 0:
                                    mask_to_show[y, x] = True
                        mask_to_show = self.smooth_cluster_shape(mask_to_show, kernel_size=5, iterations=2)
                    # Only apply colormap to positive values in the mask
                    colormap_mask = mask_to_show & (diff_cropped > 0)
                    rgba = colormap_rgba(diff, colormap_mask)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["positive"] = base64.b64encode(buf).decode('utf-8')

                # Stable cluster
                if np.sum(stable_mask) > 0:
                    rgba = colormap_rgba(diff, stable_mask)
                    rgba[~inside_polygon, 3] = 0
                    ok, buf = cv2.imencode('.tiff', rgba)
                    if ok:
                        result["stable"] = base64.b64encode(buf).decode('utf-8')

            return result

        except Exception as e:
            print(f"Error generating cluster images: {str(e)}")
            return {"negative": None, "positive": None, "stable": None}



