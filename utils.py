import os
from functools import wraps
from flask import request, jsonify
import openai
import httpx

class CustomHTTPClient(httpx.Client):
    def __init__(self, *args, **kwargs):
        kwargs.pop("proxies", None)  # Remove proxies if passed
        super().__init__(*args, **kwargs)

def token_required(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return jsonify({"status": "error", "message": "Authorization header is missing"}), 401
        token = auth_header.split(" ")[1]
        if token != os.getenv("AUTH_TOKEN"):
            return jsonify({"status": "error", "message": "Unauthorized access"}), 401
        return func(*args, **kwargs)
    return wrapper

def get_model_interpretation(index_type, positive_percentage, negative_percentage, stable_percentage, reference_date, comparison_date, locale='en'):
    # Determine which date is "better" based on positive percentage
    if positive_percentage > negative_percentage:
        better_date_statement_en = f"The **{comparison_date}** shows a more favorable outcome compared to **{reference_date}** for the **{index_type}** index."
        better_date_statement_fr = f"Le **{comparison_date}** présente un résultat plus favorable par rapport au **{reference_date}** pour l'indice **{index_type}**."
        better_date_statement_ar = f"يُظهر تاريخ **{comparison_date}** نتائج أفضل مقارنة بـ **{reference_date}** لمؤشر **{index_type}**."
    elif negative_percentage > positive_percentage:
        better_date_statement_en = f"The **{reference_date}** shows a more favorable outcome compared to **{comparison_date}** for the **{index_type}** index."
        better_date_statement_fr = f"Le **{reference_date}** présente un résultat plus favorable par rapport au **{comparison_date}** pour l'indice **{index_type}**."
        better_date_statement_ar = f"يُظهر تاريخ **{reference_date}** نتائج أفضل مقارنة بـ **{comparison_date}** لمؤشر **{index_type}**."
    else:
        better_date_statement_en = f"The **{index_type}** index shows similar outcomes between **{reference_date}** and **{comparison_date}**."
        better_date_statement_fr = f"L'indice **{index_type}** montre des résultats similaires entre le **{reference_date}** et le **{comparison_date}**."
        better_date_statement_ar = f"يُظهر مؤشر **{index_type}** نتائج مماثلة بين **{reference_date}** و **{comparison_date}**."

    if locale == 'fr':
        prompt = f"""
Vous êtes un expert en agriculture et en analyse de données.
Votre réponse DOIT commencer par la déclaration suivante, SANS aucun autre texte, en-tête ou introduction :
{better_date_statement_fr}

Ensuite, poursuivez l'interprétation en vous basant uniquement sur les données suivantes (pourcentages arrondis à deux décimales) :
- Changement positif : {positive_percentage:.2f}%
- Changement négatif : {negative_percentage:.2f}%
- Pixels stables : {stable_percentage:.2f}%

{f"L'indice {index_type} montre une progression favorable du {reference_date} au {comparison_date}" if positive_percentage > negative_percentage
else f"L'indice {index_type} reste stable sans changement significatif du {reference_date} au {comparison_date}" if positive_percentage == negative_percentage
else f"L'indice {index_type} montre une tendance défavorable du {reference_date} au {comparison_date}"}.

**REMARQUE** : assurez-vous toujours de mentionner la date de début et la date de fin dans la réponse, ainsi que s'il y a une amélioration, une diminution ou une stabilité de l'indice au cours de la période.

Ajoutez également UNE SEULE phrase simple avec des recommandations d'actions concrètes à entreprendre (maximum 2 points clés). Ne pas utiliser de puces ou de numéros pour les recommandations.
Répondez exclusivement en français.
"""
    elif locale == 'ar':
        prompt = f"""
أنت خبير في مجال الزراعة وتحليل البيانات.
يجب أن يبدأ ردك بالبيان التالي فقط، بدون أي نص آخر أو عناوين أو مقدمات:
{better_date_statement_ar}

بعد ذلك، أكمل التفسير بناءً فقط على البيانات التالية (النسب المئوية مقربة إلى منزلتين عشريتين):
- التغير الإيجابي: {positive_percentage:.2f}%
- التغير السلبي: {negative_percentage:.2f}%
- البكسلات الثابتة: {stable_percentage:.2f}%

{f"يظهر مؤشر {index_type} تحسناً ملحوظاً في الفترة من {reference_date} إلى {comparison_date}" if positive_percentage > negative_percentage
else f"يظهر مؤشر {index_type} استقراراً دون تغيير ملحوظ في الفترة من {reference_date} إلى {comparison_date}" if positive_percentage == negative_percentage
else f"يظهر مؤشر {index_type} تراجعاً ملحوظاً في الفترة من {reference_date} إلى {comparison_date}"}.

**ملاحظة**: تأكد دائمًا من ذكر تاريخ البداية وتاريخ النهاية في الإجابة، وبيّن ما إذا كان هناك تحسن، تراجع، أو استقرار في المؤشر خلال هذه الفترة.

قدم أيضًا جملة واحدة فقط تتضمن توصيات محددة للإجراءات الواجب اتخاذها (نقطتين رئيسيتين كحد أقصى). لا تستخدم النقاط أو الأرقام للتوصيات.
الرجاء الرد باللغة العربية.
"""
    else: # English locale
        prompt = f"""
You are an expert in agriculture and data analysis.
Your response MUST begin with the following statement ONLY, with no other text, headers, or introduction:
{better_date_statement_en}

Then, continue with the interpretation based solely on the following data (percentages rounded to two decimal places):
- Positive Change: {positive_percentage:.2f}%
- Negative Change: {negative_percentage:.2f}%
- Stable Pixels: {stable_percentage:.2f}%

{f"The {index_type} index shows significant enhancement from {reference_date} to {comparison_date}" if positive_percentage > negative_percentage
else f"The {index_type} index remains stable with no significant change from {reference_date} to {comparison_date}" if positive_percentage == negative_percentage
else f"The {index_type} index shows notable decline from {reference_date} to {comparison_date}"}.

**NOTE**: Always make sure to mention the reference date and comparison date in the response, and clearly indicate whether there is improvement, decrease, or stability over the period.

Also provide ONLY ONE simple sentence with specific action recommendations (maximum 2 key points). Do NOT use bullet points or numbering for recommendations.
Respond exclusively in English.
"""
    try:
        client = openai.OpenAI(
            api_key=os.environ.get("OPENAI_API_KEY"),
            http_client=CustomHTTPClient()
        )
        response = client.chat.completions.create(
            model="gpt-3.5-turbo-1106", # Or even better, try a more capable model like gpt-4 or gpt-4o if available
            messages=[
                {"role": "system", "content": "You are an expert in agricultural and environmental data analysis. Your primary goal is to provide concise and actionable interpretations of agricultural indices, following all instructions strictly."},
                {"role": "user", "content": prompt}
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        return f"Error: {str(e)}"
