# Agrivisor Flask API

## Installation

You can choose between two methods to run the Agrivisor Flask API: using Docker or a simple setup with `requirements.txt`.

### Method 1: Using Docker

1. Clone the repository:

    ```bash
    git clone <repository_url>
    cd agrivisor
    ```

2. Copy the environment file:

    ```bash
    cp .env.example .env
    ```

3. Build and run with Docker Compose:

    ```bash
    docker-compose up --build
    ```

4. Access the API locally:

    Open the following URL in your browser:

    ```
    http://localhost:5000
    ```

### Method 2: Using `requirements.txt`

1. Clone the repository:

    ```bash
    git clone https://gitlab.com/magonia/agrivisor
    cd agrivisor
    ```

2. Install the required dependencies:

    ```bash
    pip install -r requirements.txt
    ```

3. Run the Flask app:

    ```bash
    python app.py
    ```

4. Access the API locally:

    Open the following URL in your browser:

    ```
    http://localhost:5000
    ```

## Testing the API

To test the API locally or in production, you can use the following commands:

### Local Testing

```bash
python test.py --local --index_type NDVI
```

### Production Testing

```bash
python test.py --prod --index_type NDVI
```