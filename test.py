import os
import requests
import argparse
from dotenv import load_dotenv

load_dotenv()

COLOR_RED = "\033[91m"
COLOR_GREEN = "\033[92m"
COLOR_YELLOW = "\033[93m"
COLOR_CYAN = "\033[96m"
COLOR_RESET = "\033[0m"

def parse_response(response):
    try:
        return response.json()
    except ValueError as e:
        print(f"{COLOR_RED}Error parsing response: {e}{COLOR_RESET}")
        print(f"Response Content: {response.text}")  
        return None

def print_title(title, color):
    print(f"\n{color}{'='*50}{COLOR_RESET}")
    print(f"{color}{title}{COLOR_RESET}")
    print(f"{color}{'='*50}{COLOR_RESET}")

def test_interpretation_endpoint(url, folder_path, index_input, locale):
    folder_name = os.path.basename(folder_path)
    print_title(f"Testing Folder: {folder_name}", COLOR_CYAN)

    auth_token = os.environ.get("AUTH_TOKEN")
    if not auth_token:
        print(f"{COLOR_RED}Authorization token is missing! Please set the AUTH_TOKEN environment variable.{COLOR_RESET}")
        return

    headers = {
        "Authorization": f"Bearer {auth_token}"
    }

    try:
        ref_file = next((f for f in os.listdir(folder_path) if f.startswith("reference-")), None)
        comp_file = next((f for f in os.listdir(folder_path) if f.startswith("comparison-")), None)

        if not ref_file or not comp_file:
            print(f"{COLOR_YELLOW}Skipping {folder_name}: Missing reference or comparison file.{COLOR_RESET}")
            return

        with open(os.path.join(folder_path, ref_file), "rb") as f_ref, \
             open(os.path.join(folder_path, comp_file), "rb") as f_comp:

            files = [("images", f_ref), ("images", f_comp)]
            data = {
                "reference": 0,
                "comparaison": 1,
                "index_type": index_input,
                "locale": locale,
                "reference_date": "2025-01-01",        # You can parse real dates from filenames if needed
                "comparison_date": "2025-02-01",
                "polygon": "[[[2.225697,48.12919],[2.226534,48.125995],[2.219882,48.125035],[2.219603,48.125881],[2.219324,48.126941],[2.219217,48.127485],[2.218852,48.128159],[2.225697,48.12919]]]"
            }

            response = requests.post(url, files=files, data=data, headers=headers)

    except FileNotFoundError as e:
        print(f"{COLOR_RED}Error: Image file not found in {folder_path}!{COLOR_RESET}", e)
        return

    result = parse_response(response)
    if result:
        print("Full Response (filtered):", {k: result[k] for k in result if k not in ["difference"]})
        print("\nInterpretation Results:")
        print(f"Index Type: {result.get('index_type', 'N/A')}")
        print(f"Positive Percentage: {result.get('positive_percentage', 'N/A')}%")
        print(f"Negative Percentage: {result.get('negative_percentage', 'N/A')}%")
        print(f"Stable Percentage: {result.get('stable_percentage', 'N/A')}%")
        print("\nInterpretation:")
        print(result.get("model_interpretation", "N/A"))
    else:
        print(f"{COLOR_RED}No valid result received from the API.{COLOR_RESET}")

if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Loop through folders and test Interpretation Endpoint.")
    
    parser.add_argument("--local", action="store_true", help="Use local environment URL (default is local).")
    parser.add_argument("--prod", action="store_true", help="Use production environment URL.")
    
    parser.add_argument("--index_type", type=str, required=True,
                        choices=["NDVI", "NDWI", "ARI1", "CAR_RE", "CCC", "CL_RE", "CWC", "EVI", "FAPAR",
                                 "IRECI", "LAI", "MNDWI", "MSAVI2", "NDMI", "NDRE", "NDREX", "NMDI",
                                 "NMDI_SOIL", "NMDI_VEG", "SI", "SOC_SWIR", "SOC_VIS", "SMI", "WIW", "YRSI"],
                        help="The index type to be used in the request")
    
    parser.add_argument("--locale", type=str, default="en", choices=["en", "ar", "fr"],
                        help="Locale for the interpretation (choose from: en, ar, fr)")

    args = parser.parse_args()

    if args.local:
        url = "http://127.0.0.1:5000/interpretation"
    elif args.prod:
        url = "https://agri-visor-77744321532.europe-west3.run.app/interpretation"
    else:
        url = "http://127.0.0.1:5000/interpretation"

    base_image_dir = "images"

    for subfolder in os.listdir(base_image_dir):
        folder_path = os.path.join(base_image_dir, subfolder)
        if os.path.isdir(folder_path):
            test_interpretation_endpoint(url, folder_path, args.index_type, args.locale)