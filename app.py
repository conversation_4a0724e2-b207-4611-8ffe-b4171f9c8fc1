import os
import tempfile
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
from werkzeug.utils import secure_filename
from utils import get_model_interpretation, token_required
from change_detection import ChangeDetectionModel
import json

# Load environment variables from the .env file
load_dotenv()

# Initialize Flask app and enable CORS
app = Flask(__name__)
CORS(app)

# Single endpoint that returns interpretation, change percentages, and difference image
@app.route('/interpretation', methods=['POST'])
@token_required
def generate_interpretation():
    try:
        files = request.files.getlist('images')
        week1 = int(request.form.get('reference'))
        week2 = int(request.form.get('comparaison'))
        index_type = request.form.get('index_type', 'NDVI').upper()
        locale = request.form.get('locale', 'en').lower()

        # Get threshold parameter from request (default to 0.0 if not provided)
        try:
            threshold = float(request.form.get('threshold', 0.0))
            # Ensure threshold is between 0.0 and 1.0
            threshold = max(0.0, min(1.0, threshold))
        except (ValueError, TypeError):
            threshold = 0.0

        # Get polygon coordinates from request
        polygon_coords = None
        polygon_input = request.form.get('polygon')
        if polygon_input:
            try:
                polygon_data = json.loads(polygon_input)
                polygon_coords = [(float(coord[0]), float(coord[1])) for coord in polygon_data[0]]
            except Exception as e:
                return jsonify({"status": "error", "message": f"Error with polygon format: {str(e)}"}), 400

        # Date fields for interpretation
        reference_date = request.form.get('reference_date', 'unknown')
        comparison_date = request.form.get('comparison_date', 'unknown')

        # Prepare temporary files for geospatial reprojection
        file1 = files[week1]
        file2 = files[week2]
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=secure_filename(file1.filename)) as tmp1, \
             tempfile.NamedTemporaryFile(delete=False, suffix=secure_filename(file2.filename)) as tmp2:
            tmp1.write(file1.read())
            tmp1.flush()
            tmp2.write(file2.read())
            tmp2.flush()

            # Initialize and configure model
            model = ChangeDetectionModel()
            model.set_thresholds_for_index(index_type)
            # Calculate difference using geospatial reprojection
            diff, metadata = model.calculate_difference(tmp1.name, tmp2.name, polygon_coords)

        # Clean up temp files
        os.remove(tmp1.name)
        os.remove(tmp2.name)

        # Classify changes and interpret with threshold applied
        classification = model.classify_changes(diff, metadata, threshold)
        model_interpretation = get_model_interpretation(
            index_type,
            classification["positive_percentage"],
            classification["negative_percentage"],
            classification["stable_percentage"],
            reference_date,
            comparison_date,
            locale
        )

        # Encode colored diff image with threshold applied
        diff_base64 = model.generate_colored_difference(diff, metadata, threshold)

        # Generate cluster images for negative, positive, and stable zones with threshold applied
        cluster_images = model.generate_cluster_image(diff, metadata, n_clusters=3, threshold=threshold)

        # Prepare cluster images for response
        negative_cluster = f"data:image/tiff;base64,{cluster_images['negative']}" if cluster_images['negative'] else None
        positive_cluster = f"data:image/tiff;base64,{cluster_images['positive']}" if cluster_images['positive'] else None
        stable_cluster = f"data:image/tiff;base64,{cluster_images['stable']}" if cluster_images['stable'] else None
        all_clusters = f"data:image/tiff;base64,{cluster_images['all_clusters']}" if cluster_images['all_clusters'] else None


        return jsonify({
            "status": "success",
            "index_type": index_type,
            "threshold": threshold,
            **classification,
            "difference": diff.tolist(),
            "tiff_image": f"data:image/tiff;base64,{diff_base64}",
            "negative_cluster": negative_cluster,
            "positive_cluster": positive_cluster,
            "stable_cluster": stable_cluster,
            "all_clusters": all_clusters,
            "model_interpretation": model_interpretation
        })

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/ping', methods=['GET'])
@token_required
def ping():
    return jsonify({"status": "success", "message": "Pong! The server is alive."})

if __name__ == '__main__':
    app.run(debug=True)