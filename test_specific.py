import os
import requests
import argparse
from dotenv import load_dotenv
import base64
from PIL import Image
import io
import matplotlib.pyplot as plt
import numpy as np

load_dotenv()

COLOR_RED = "\033[91m"
COLOR_GREEN = "\033[92m"
COLOR_YELLOW = "\033[93m"
COLOR_CYAN = "\033[96m"
COLOR_RESET = "\033[0m"

def parse_response(response):
    try:
        return response.json()
    except ValueError as e:
        print(f"{COLOR_RED}Error parsing response: {e}{COLOR_RESET}")
        print(f"Response Content: {response.text}")
        return None

def print_title(title, color):
    print(f"\n{color}{'='*50}{COLOR_RESET}")
    print(f"{color}{title}{COLOR_RESET}")
    print(f"{color}{'='*50}{COLOR_RESET}")

def save_image(base64_str, output_path):
    """Save a base64 encoded image to a file"""
    if not base64_str:
        print(f"{COLOR_YELLOW}No image data to save{COLOR_RESET}")
        return False

    try:
        # Remove data URL prefix if present
        if "base64," in base64_str:
            base64_str = base64_str.split("base64,")[1]

        # Decode base64 string
        image_data = base64.b64decode(base64_str)

        # Save the image
        with open(output_path, "wb") as f:
            f.write(image_data)

        print(f"{COLOR_GREEN}Image saved to {output_path}{COLOR_RESET}")
        return True
    except Exception as e:
        print(f"{COLOR_RED}Error saving image: {str(e)}{COLOR_RESET}")
        return False

def test_interpretation_endpoint(url, folder_path, index_input, locale, threshold=0.2, save_images=True):
    folder_name = os.path.basename(folder_path)
    print_title(f"Testing Folder: {folder_name}", COLOR_CYAN)

    auth_token = os.environ.get("AUTH_TOKEN")
    if not auth_token:
        print(f"{COLOR_RED}Authorization token is missing! Please set the AUTH_TOKEN environment variable.{COLOR_RESET}")
        return

    headers = {
        "Authorization": f"Bearer {auth_token}"
    }

    try:
        ref_file = next((f for f in os.listdir(folder_path) if f.startswith("reference-")), None)
        comp_file = next((f for f in os.listdir(folder_path) if f.startswith("comparison-")), None)

        if not ref_file or not comp_file:
            print(f"{COLOR_YELLOW}Skipping {folder_name}: Missing reference or comparison file.{COLOR_RESET}")
            return

        with open(os.path.join(folder_path, ref_file), "rb") as f_ref, \
             open(os.path.join(folder_path, comp_file), "rb") as f_comp:

            files = [("images", f_ref), ("images", f_comp)]
            data = {
                "reference": 0,
                "comparaison": 1,
                "index_type": index_input,
                "locale": locale,
                "reference_date": "2025-01-01",
                "comparison_date": "2025-02-01",
                "threshold": threshold,
                "polygon": "[[[1.520344,48.181266],[1.520301,48.181652],[1.520752,48.182812],[1.527599,48.182497],[1.526698,48.180207],[1.520902,48.180264],[1.521224,48.180951],[1.520344,48.181266]]]"
            }

            response = requests.post(url, files=files, data=data, headers=headers)

    except FileNotFoundError as e:
        print(f"{COLOR_RED}Error: Image file not found in {folder_path}!{COLOR_RESET}", e)
        return

    result = parse_response(response)
    if result:
        # Filter out large base64 strings from the printed response
        filtered_result = {k: result[k] for k in result if k not in ["tiff_image", "negative_cluster", "positive_cluster", "stable_cluster", "cluster_image"]}
        print("Response:", filtered_result)

        print("\nInterpretation Results:")
        print(f"Index Type: {result.get('index_type', 'N/A')}")
        print(f"Threshold: {result.get('threshold', 0.0)}")
        print(f"Positive Percentage: {result.get('positive_percentage', 'N/A')}%")
        print(f"Negative Percentage: {result.get('negative_percentage', 'N/A')}%")
        print(f"Stable Percentage: {result.get('stable_percentage', 'N/A')}%")

        print("\nImages:")
        print(f"PNG Image URL: {result.get('png_image_url', 'Not available')}")
        print(f"Negative Cluster: {'Available' if result.get('negative_cluster') else 'Not available'}")
        print(f"Positive Cluster: {'Available' if result.get('positive_cluster') else 'Not available'}")
        print(f"Stable Cluster: {'Available' if result.get('stable_cluster') else 'Not available'}")

        print("\nInterpretation:")
        print(result.get("model_interpretation", "N/A"))

        # Save images if requested
        if save_images:
            output_dir = os.path.join("output", folder_name)
            os.makedirs(output_dir, exist_ok=True)

            # Save difference image
            if "tiff_image" in result and result["tiff_image"]:
                diff_path = os.path.join(output_dir, f"{index_input}_diff.tiff")
                save_image(result["tiff_image"], diff_path)

            # Save negative cluster image
            if "negative_cluster" in result and result["negative_cluster"]:
                neg_cluster_path = os.path.join(output_dir, f"{index_input}_negative_cluster.tiff")
                save_image(result["negative_cluster"], neg_cluster_path)

            # Save positive cluster image
            if "positive_cluster" in result and result["positive_cluster"]:
                pos_cluster_path = os.path.join(output_dir, f"{index_input}_positive_cluster.tiff")
                save_image(result["positive_cluster"], pos_cluster_path)

            # Save stable cluster image
            if "stable_cluster" in result and result["stable_cluster"]:
                stable_cluster_path = os.path.join(output_dir, f"{index_input}_stable_cluster.tiff")
                save_image(result["stable_cluster"], stable_cluster_path)

            # For backward compatibility, check for the old cluster_image field
            if "cluster_image" in result and result["cluster_image"]:
                cluster_path = os.path.join(output_dir, f"{index_input}_cluster.tiff")
                save_image(result["cluster_image"], cluster_path)

        return result
    else:
        print(f"{COLOR_RED}No valid result received from the API.{COLOR_RESET}")
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test specific folders with the Interpretation Endpoint.")

    parser.add_argument("--local", action="store_true", help="Use local environment URL (default is local).")
    parser.add_argument("--prod", action="store_true", help="Use production environment URL.")

    parser.add_argument("--index_type", type=str, required=True,
                        choices=["NDVI", "NDWI", "ARI1", "CAR_RE", "CCC", "CL_RE", "CWC", "EVI", "FAPAR",
                                 "IRECI", "LAI", "MNDWI", "MSAVI2", "NDMI", "NDRE", "NDREX", "NMDI",
                                 "NMDI_SOIL", "NMDI_VEG", "SI", "SOC_SWIR", "SOC_VIS", "SMI", "WIW", "YRSI"],
                        help="The index type to be used in the request")

    parser.add_argument("--locale", type=str, default="en", choices=["en", "ar", "fr"],
                        help="Locale for the interpretation (choose from: en, ar, fr)")

    parser.add_argument("--threshold", type=float, default=0.0,
                        help="Threshold value (0.0-1.0) to filter out pixels with values below the threshold")

    parser.add_argument("--no-save", action="store_true", help="Don't save output images")

    args = parser.parse_args()

    if args.local:
        url = "http://127.0.0.1:5000/interpretation"
    elif args.prod:
        url = "https://agri-visor-77744321532.europe-west3.run.app/interpretation"
    else:
        url = "http://127.0.0.1:5000/interpretation"

    # Test only the specific folders
    specific_folders = ["Area_creation_potatoes_01"]
    base_image_dir = "images"

    for folder_name in specific_folders:
        folder_path = os.path.join(base_image_dir, folder_name)
        if os.path.isdir(folder_path):
            test_interpretation_endpoint(url, folder_path, args.index_type, args.locale, args.threshold, not args.no_save)
        else:
            print(f"{COLOR_YELLOW}Folder not found: {folder_path}{COLOR_RESET}")
